# E-Ticaret Session Value Tahmin Modeli - <PERSON>zet Rapor

## Proje Özeti
Bu proje, e-ticaret sitesindeki kullanıcı session'larının değerini tahmin etmeyi amaçlamaktadır. 100.000+ satırlık veri seti üzerinde <PERSON>, kull<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, sepete <PERSON><PERSON>, satın alma gibi davranışlarından session değeri tahmin edilmiştir.

## Veri Seti Bilgileri
- **Train Veri**: 141,219 satır, 7 sütun
- **Test Veri**: 62,951 satır, 6 sütun
- **He<PERSON><PERSON>**: session_value (5.38 - 2,328.66 aralığında)

### Event Type Dağılımı
- VIEW: 58,829 (%41.6)
- ADD_CART: 42,304 (%30.0)
- REMOVE_CART: 25,615 (%18.1)
- BUY: 14,471 (%10.2)

## Özel<PERSON> Mühendisliği

### <PERSON><PERSON> Özellikler
- **Zaman Özellikleri**: <PERSON><PERSON>, g<PERSON><PERSON>, ay, hafta sonu kontrolü
- **Session Metrikleri**: Toplam event sayısı, benzersiz ürün/kategori sayısı
- **Event Sayıları**: Her event türü için sayım ve oranlar

### Gelişmiş Özellikler
- **Session Süresi**: İlk ve son event arasındaki süre
- **Çeşitlilik Metrikleri**: Kategori ve ürün çeşitliliği
- **Sequence Özellikleri**: İlk/son event, satın alma oranları
- **Zaman Dilimi Özellikleri**: Gece, sabah, öğleden sonra, akşam aktiviteleri

## Model Performansları

### 1. Temel Model (LightGBM)
- **RMSE**: 21.93
- **MAE**: 11.32
- **R²**: 0.8078

### 2. Gelişmiş Model (Optimized LightGBM)
- **RMSE**: 21.54
- **MAE**: 11.33
- **R²**: 0.8146
- **5-fold CV RMSE**: 19.56

### 3. Ensemble Model (Stacking)
- **RMSE**: 19.52 ⭐ **En İyi**
- **MAE**: 11.22
- **R²**: 0.8315

#### Base Model Performansları:
- LightGBM: RMSE=19.75, R²=0.8275
- XGBoost: RMSE=20.11, R²=0.8212
- Random Forest: RMSE=19.94, R²=0.8241

#### Meta Model Ağırlıkları:
- LightGBM: 49.8%
- Random Forest: 33.1%
- XGBoost: 18.4%

## En Önemli Özellikler
1. **count_BUY**: Satın alma sayısı
2. **session_duration_minutes**: Session süresi
3. **unique_categories**: Benzersiz kategori sayısı
4. **total_events**: Toplam event sayısı
5. **count_ADD_CART**: Sepete ekleme sayısı

## Sonuçlar ve Öneriler

### Başarılar
- ✅ %83.15 R² skoru ile yüksek açıklama gücü
- ✅ Ensemble yaklaşımı ile model performansında iyileşme
- ✅ Kapsamlı özellik mühendisliği
- ✅ Cross-validation ile güvenilir performans değerlendirmesi

### Teknik Detaylar
- **Hiperparametre Optimizasyonu**: GridSearchCV ile LightGBM parametreleri optimize edildi
- **Stacking Ensemble**: 5-fold cross-validation ile out-of-fold predictions
- **Meta Model**: Ridge regression ile base model tahminlerini birleştirme

### Gelecek İyileştirmeler
1. **Daha Fazla Özellik**: Kullanıcı geçmişi, mevsimsel etkiler
2. **Deep Learning**: Neural network modelleri deneme
3. **Feature Selection**: Daha sofistike özellik seçimi teknikleri
4. **Hyperparameter Tuning**: Bayesian optimization kullanımı

## Dosyalar
- `session_value_prediction.py`: Temel model
- `advanced_session_prediction.py`: Gelişmiş model
- `ensemble_model.py`: Ensemble model
- `submission.csv`: Temel model tahminleri
- `advanced_submission.csv`: Gelişmiş model tahminleri
- `ensemble_submission.csv`: **Final submission** (En iyi performans)

## Kullanım
```bash
# Temel model
python3 session_value_prediction.py

# Gelişmiş model
python3 advanced_session_prediction.py

# Ensemble model (Önerilen)
python3 ensemble_model.py
```

**Final Submission**: `ensemble_submission.csv` dosyası Kaggle'a submit edilmelidir.
