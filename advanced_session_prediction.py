import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.preprocessing import LabelEncoder, StandardScaler
import xgboost as xgb
import lightgbm as lgb
import warnings
warnings.filterwarnings('ignore')

# Veri yükleme
print("Veri yükleniyor...")
train_df = pd.read_csv('train.csv')
test_df = pd.read_csv('test.csv')

# Zaman sütununu datetime'a çevir
train_df['event_time'] = pd.to_datetime(train_df['event_time'])
test_df['event_time'] = pd.to_datetime(test_df['event_time'])

# Gelişmiş özellik mühendisliği
def advanced_feature_engineering(df):
    # Zaman özelliklerini çıkar
    df['hour'] = df['event_time'].dt.hour
    df['day_of_week'] = df['event_time'].dt.dayofweek
    df['day_of_month'] = df['event_time'].dt.day
    df['month'] = df['event_time'].dt.month
    df['is_weekend'] = (df['day_of_week'] >= 5).astype(int)
    
    # Saat dilimlerini kategorize et
    df['time_period'] = pd.cut(df['hour'], 
                              bins=[0, 6, 12, 18, 24], 
                              labels=['night', 'morning', 'afternoon', 'evening'],
                              include_lowest=True)
    
    return df

train_df = advanced_feature_engineering(train_df)
test_df = advanced_feature_engineering(test_df)

# Session bazında gelişmiş özellik mühendisliği
def create_advanced_session_features(df):
    # Temel session özellikleri
    session_features = df.groupby('user_session').agg({
        'event_type': ['count', 'nunique'],
        'product_id': 'nunique',
        'category_id': 'nunique',
        'user_id': 'first',
        'hour': ['min', 'max', 'mean', 'std'],
        'day_of_week': 'first',
        'day_of_month': 'first',
        'month': 'first',
        'is_weekend': 'first'
    }).reset_index()
    
    # Sütun isimlerini düzenle
    session_features.columns = ['user_session', 'total_events', 'unique_event_types',
                               'unique_products', 'unique_categories', 'user_id',
                               'min_hour', 'max_hour', 'mean_hour', 'std_hour',
                               'day_of_week', 'day_of_month', 'month', 'is_weekend']
    
    # Event type bazında sayılar ve oranlar
    event_counts = df.groupby(['user_session', 'event_type']).size().unstack(fill_value=0)
    event_counts.columns = [f'count_{col}' for col in event_counts.columns]
    
    # Event oranları
    for col in event_counts.columns:
        event_counts[f'ratio_{col.split("_")[1]}'] = event_counts[col] / event_counts.sum(axis=1)
    
    event_counts = event_counts.reset_index()
    
    # Time period bazında özellikler
    time_period_counts = df.groupby(['user_session', 'time_period']).size().unstack(fill_value=0)
    time_period_counts.columns = [f'time_{col}' for col in time_period_counts.columns]
    time_period_counts = time_period_counts.reset_index()
    
    # Session süresi (ilk ve son event arasındaki fark)
    session_duration = df.groupby('user_session')['event_time'].agg(['min', 'max']).reset_index()
    session_duration['session_duration_minutes'] = (session_duration['max'] - session_duration['min']).dt.total_seconds() / 60
    session_duration = session_duration[['user_session', 'session_duration_minutes']]
    
    # Kategori çeşitliliği
    category_diversity = df.groupby('user_session')['category_id'].apply(lambda x: len(set(x)) / len(x)).reset_index()
    category_diversity.columns = ['user_session', 'category_diversity']
    
    # Ürün çeşitliliği
    product_diversity = df.groupby('user_session')['product_id'].apply(lambda x: len(set(x)) / len(x)).reset_index()
    product_diversity.columns = ['user_session', 'product_diversity']
    
    # Event sequence özellikleri
    def get_sequence_features(group):
        events = group['event_type'].tolist()
        return pd.Series({
            'first_event': events[0],
            'last_event': events[-1],
            'has_buy': 'BUY' in events,
            'buy_ratio': events.count('BUY') / len(events),
            'view_to_cart_ratio': events.count('ADD_CART') / max(events.count('VIEW'), 1),
            'cart_to_buy_ratio': events.count('BUY') / max(events.count('ADD_CART'), 1)
        })
    
    sequence_features = df.groupby('user_session').apply(get_sequence_features).reset_index()
    
    # Tüm özellikleri birleştir
    session_features = session_features.merge(event_counts, on='user_session', how='left')
    session_features = session_features.merge(time_period_counts, on='user_session', how='left')
    session_features = session_features.merge(session_duration, on='user_session', how='left')
    session_features = session_features.merge(category_diversity, on='user_session', how='left')
    session_features = session_features.merge(product_diversity, on='user_session', how='left')
    session_features = session_features.merge(sequence_features, on='user_session', how='left')
    
    # Eksik değerleri doldur
    session_features = session_features.fillna(0)
    
    return session_features

print("Gelişmiş session özelliklerini oluşturuyor...")
train_session_features = create_advanced_session_features(train_df)
test_session_features = create_advanced_session_features(test_df)

# Target değerini ekle
train_targets = train_df.groupby('user_session')['session_value'].first().reset_index()
train_session_features = train_session_features.merge(train_targets, on='user_session', how='left')

print(f"Train session features boyutu: {train_session_features.shape}")
print(f"Test session features boyutu: {test_session_features.shape}")

# Kategorik değişkenleri encode et
categorical_cols = ['user_id', 'first_event', 'last_event']
label_encoders = {}

for col in categorical_cols:
    if col in train_session_features.columns:
        le = LabelEncoder()
        # Train ve test'i birleştirerek fit et
        all_values = pd.concat([train_session_features[col], test_session_features[col]]).astype(str)
        le.fit(all_values)
        
        train_session_features[col] = le.transform(train_session_features[col].astype(str))
        test_session_features[col] = le.transform(test_session_features[col].astype(str))
        
        label_encoders[col] = le

# Boolean sütunları int'e çevir
bool_cols = ['has_buy']
for col in bool_cols:
    if col in train_session_features.columns:
        train_session_features[col] = train_session_features[col].astype(int)
        test_session_features[col] = test_session_features[col].astype(int)

# Özellik ve hedef değişkenleri ayır
feature_cols = [col for col in train_session_features.columns if col not in ['user_session', 'session_value']]
X = train_session_features[feature_cols]
y = train_session_features['session_value']
X_test = test_session_features[feature_cols]

print(f"\nÖzellik sayısı: {len(feature_cols)}")

# Train-validation split
X_train, X_val, y_train, y_val = train_test_split(X, y, test_size=0.2, random_state=42)

# Model eğitimi ve optimizasyonu
print("\n=== GELİŞMİŞ MODEL EĞİTİMİ ===")

# LightGBM hiperparametre optimizasyonu
print("LightGBM hiperparametre optimizasyonu...")
lgb_params = {
    'n_estimators': [200, 300],
    'max_depth': [6, 8, 10],
    'learning_rate': [0.05, 0.1],
    'num_leaves': [31, 50],
    'min_child_samples': [20, 30]
}

lgb_model = lgb.LGBMRegressor(random_state=42, n_jobs=-1, verbose=-1)
lgb_grid = GridSearchCV(lgb_model, lgb_params, cv=3, scoring='neg_mean_squared_error', n_jobs=-1, verbose=1)
lgb_grid.fit(X_train, y_train)

print(f"En iyi LightGBM parametreleri: {lgb_grid.best_params_}")

# En iyi modeli kullanarak tahmin
best_lgb = lgb_grid.best_estimator_
lgb_pred_val = best_lgb.predict(X_val)
lgb_rmse = np.sqrt(mean_squared_error(y_val, lgb_pred_val))
lgb_mae = mean_absolute_error(y_val, lgb_pred_val)
lgb_r2 = r2_score(y_val, lgb_pred_val)

print(f"Optimized LightGBM - RMSE: {lgb_rmse:.4f}, MAE: {lgb_mae:.4f}, R2: {lgb_r2:.4f}")

# Özellik önemini görselleştir
feature_importance = pd.DataFrame({
    'feature': feature_cols,
    'importance': best_lgb.feature_importances_
}).sort_values('importance', ascending=False)

print("\nEn önemli 15 özellik:")
print(feature_importance.head(15))

# Test tahminleri
print("\nTest tahminleri yapılıyor...")
test_predictions = best_lgb.predict(X_test)

# Submission dosyası oluştur
submission = pd.DataFrame({
    'user_session': test_session_features['user_session'],
    'session_value': test_predictions
})

submission.to_csv('advanced_submission.csv', index=False)
print("Gelişmiş submission dosyası 'advanced_submission.csv' olarak kaydedildi.")

print(f"\nTahmin istatistikleri:")
print(f"Min: {test_predictions.min():.4f}")
print(f"Max: {test_predictions.max():.4f}")
print(f"Mean: {test_predictions.mean():.4f}")
print(f"Std: {test_predictions.std():.4f}")

# Cross-validation skoru
cv_scores = cross_val_score(best_lgb, X, y, cv=5, scoring='neg_mean_squared_error')
print(f"\n5-fold CV RMSE: {np.sqrt(-cv_scores.mean()):.4f} (+/- {np.sqrt(cv_scores.std() * 2):.4f})")
