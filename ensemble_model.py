import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, KFold
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.preprocessing import LabelEncoder
import xgboost as xgb
import lightgbm as lgb
from sklearn.linear_model import Ridge
import warnings
warnings.filterwarnings('ignore')

# Veri yükleme ve özellik mühendisliği (önceki koddan)
print("Veri yükleniyor...")
train_df = pd.read_csv('train.csv')
test_df = pd.read_csv('test.csv')

# Zaman sütununu datetime'a çevir
train_df['event_time'] = pd.to_datetime(train_df['event_time'])
test_df['event_time'] = pd.to_datetime(test_df['event_time'])

def advanced_feature_engineering(df):
    df['hour'] = df['event_time'].dt.hour
    df['day_of_week'] = df['event_time'].dt.dayofweek
    df['day_of_month'] = df['event_time'].dt.day
    df['month'] = df['event_time'].dt.month
    df['is_weekend'] = (df['day_of_week'] >= 5).astype(int)
    df['time_period'] = pd.cut(df['hour'], 
                              bins=[0, 6, 12, 18, 24], 
                              labels=['night', 'morning', 'afternoon', 'evening'],
                              include_lowest=True)
    return df

def create_advanced_session_features(df):
    # Temel session özellikleri
    session_features = df.groupby('user_session').agg({
        'event_type': ['count', 'nunique'],
        'product_id': 'nunique',
        'category_id': 'nunique',
        'user_id': 'first',
        'hour': ['min', 'max', 'mean', 'std'],
        'day_of_week': 'first',
        'day_of_month': 'first',
        'month': 'first',
        'is_weekend': 'first'
    }).reset_index()
    
    session_features.columns = ['user_session', 'total_events', 'unique_event_types',
                               'unique_products', 'unique_categories', 'user_id',
                               'min_hour', 'max_hour', 'mean_hour', 'std_hour',
                               'day_of_week', 'day_of_month', 'month', 'is_weekend']
    
    # Event type bazında sayılar ve oranlar
    event_counts = df.groupby(['user_session', 'event_type']).size().unstack(fill_value=0)
    event_counts.columns = [f'count_{col}' for col in event_counts.columns]
    
    for col in event_counts.columns:
        event_counts[f'ratio_{col.split("_")[1]}'] = event_counts[col] / event_counts.sum(axis=1)
    
    event_counts = event_counts.reset_index()
    
    # Time period bazında özellikler
    time_period_counts = df.groupby(['user_session', 'time_period']).size().unstack(fill_value=0)
    time_period_counts.columns = [f'time_{col}' for col in time_period_counts.columns]
    time_period_counts = time_period_counts.reset_index()
    
    # Session süresi
    session_duration = df.groupby('user_session')['event_time'].agg(['min', 'max']).reset_index()
    session_duration['session_duration_minutes'] = (session_duration['max'] - session_duration['min']).dt.total_seconds() / 60
    session_duration = session_duration[['user_session', 'session_duration_minutes']]
    
    # Çeşitlilik metrikleri
    category_diversity = df.groupby('user_session')['category_id'].apply(lambda x: len(set(x)) / len(x)).reset_index()
    category_diversity.columns = ['user_session', 'category_diversity']
    
    product_diversity = df.groupby('user_session')['product_id'].apply(lambda x: len(set(x)) / len(x)).reset_index()
    product_diversity.columns = ['user_session', 'product_diversity']
    
    # Event sequence özellikleri
    def get_sequence_features(group):
        events = group['event_type'].tolist()
        return pd.Series({
            'first_event': events[0],
            'last_event': events[-1],
            'has_buy': 'BUY' in events,
            'buy_ratio': events.count('BUY') / len(events),
            'view_to_cart_ratio': events.count('ADD_CART') / max(events.count('VIEW'), 1),
            'cart_to_buy_ratio': events.count('BUY') / max(events.count('ADD_CART'), 1)
        })
    
    sequence_features = df.groupby('user_session').apply(get_sequence_features).reset_index()
    
    # Tüm özellikleri birleştir
    session_features = session_features.merge(event_counts, on='user_session', how='left')
    session_features = session_features.merge(time_period_counts, on='user_session', how='left')
    session_features = session_features.merge(session_duration, on='user_session', how='left')
    session_features = session_features.merge(category_diversity, on='user_session', how='left')
    session_features = session_features.merge(product_diversity, on='user_session', how='left')
    session_features = session_features.merge(sequence_features, on='user_session', how='left')
    
    session_features = session_features.fillna(0)
    return session_features

train_df = advanced_feature_engineering(train_df)
test_df = advanced_feature_engineering(test_df)

print("Session özelliklerini oluşturuyor...")
train_session_features = create_advanced_session_features(train_df)
test_session_features = create_advanced_session_features(test_df)

# Target değerini ekle
train_targets = train_df.groupby('user_session')['session_value'].first().reset_index()
train_session_features = train_session_features.merge(train_targets, on='user_session', how='left')

# Kategorik değişkenleri encode et
categorical_cols = ['user_id', 'first_event', 'last_event']
label_encoders = {}

for col in categorical_cols:
    if col in train_session_features.columns:
        le = LabelEncoder()
        all_values = pd.concat([train_session_features[col], test_session_features[col]]).astype(str)
        le.fit(all_values)
        
        train_session_features[col] = le.transform(train_session_features[col].astype(str))
        test_session_features[col] = le.transform(test_session_features[col].astype(str))
        
        label_encoders[col] = le

# Boolean sütunları int'e çevir
bool_cols = ['has_buy']
for col in bool_cols:
    if col in train_session_features.columns:
        train_session_features[col] = train_session_features[col].astype(int)
        test_session_features[col] = test_session_features[col].astype(int)

# Özellik ve hedef değişkenleri ayır
feature_cols = [col for col in train_session_features.columns if col not in ['user_session', 'session_value']]
X = train_session_features[feature_cols]
y = train_session_features['session_value']
X_test = test_session_features[feature_cols]

print(f"Özellik sayısı: {len(feature_cols)}")

# Ensemble Model - Stacking
print("\n=== ENSEMBLE MODEL (STACKING) ===")

# Base modeller
base_models = {
    'lgb': lgb.LGBMRegressor(n_estimators=200, max_depth=8, learning_rate=0.05, 
                            num_leaves=31, min_child_samples=20, random_state=42, 
                            n_jobs=-1, verbose=-1),
    'xgb': xgb.XGBRegressor(n_estimators=200, max_depth=6, learning_rate=0.05, 
                           random_state=42, n_jobs=-1),
    'rf': RandomForestRegressor(n_estimators=200, max_depth=10, random_state=42, n_jobs=-1)
}

# Meta model
meta_model = Ridge(alpha=1.0)

# K-fold stacking
kf = KFold(n_splits=5, shuffle=True, random_state=42)
oof_predictions = np.zeros((len(X), len(base_models)))
test_predictions = np.zeros((len(X_test), len(base_models)))

print("Base modeller eğitiliyor...")
for i, (model_name, model) in enumerate(base_models.items()):
    print(f"  {model_name} eğitiliyor...")
    
    # Out-of-fold predictions
    for fold, (train_idx, val_idx) in enumerate(kf.split(X)):
        X_train_fold, X_val_fold = X.iloc[train_idx], X.iloc[val_idx]
        y_train_fold, y_val_fold = y.iloc[train_idx], y.iloc[val_idx]
        
        model.fit(X_train_fold, y_train_fold)
        oof_predictions[val_idx, i] = model.predict(X_val_fold)
    
    # Full model for test predictions
    model.fit(X, y)
    test_predictions[:, i] = model.predict(X_test)

# Meta model eğitimi
print("Meta model eğitiliyor...")
meta_model.fit(oof_predictions, y)

# Final predictions
final_test_predictions = meta_model.predict(test_predictions)

# Validation performance
oof_final_predictions = meta_model.predict(oof_predictions)
ensemble_rmse = np.sqrt(mean_squared_error(y, oof_final_predictions))
ensemble_mae = mean_absolute_error(y, oof_final_predictions)
ensemble_r2 = r2_score(y, oof_final_predictions)

print(f"\nEnsemble Model Performance:")
print(f"RMSE: {ensemble_rmse:.4f}")
print(f"MAE: {ensemble_mae:.4f}")
print(f"R2: {ensemble_r2:.4f}")

# Base model performansları
print("\nBase Model Performances (OOF):")
for i, (model_name, _) in enumerate(base_models.items()):
    base_rmse = np.sqrt(mean_squared_error(y, oof_predictions[:, i]))
    base_mae = mean_absolute_error(y, oof_predictions[:, i])
    base_r2 = r2_score(y, oof_predictions[:, i])
    print(f"{model_name}: RMSE={base_rmse:.4f}, MAE={base_mae:.4f}, R2={base_r2:.4f}")

# Submission dosyası oluştur
submission = pd.DataFrame({
    'user_session': test_session_features['user_session'],
    'session_value': final_test_predictions
})

submission.to_csv('ensemble_submission.csv', index=False)
print("\nEnsemble submission dosyası 'ensemble_submission.csv' olarak kaydedildi.")

print(f"\nFinal tahmin istatistikleri:")
print(f"Min: {final_test_predictions.min():.4f}")
print(f"Max: {final_test_predictions.max():.4f}")
print(f"Mean: {final_test_predictions.mean():.4f}")
print(f"Std: {final_test_predictions.std():.4f}")

# Model ağırlıklarını göster
print(f"\nMeta model katsayıları:")
for i, (model_name, _) in enumerate(base_models.items()):
    print(f"{model_name}: {meta_model.coef_[i]:.4f}")
print(f"Intercept: {meta_model.intercept_:.4f}")
