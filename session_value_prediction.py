import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.preprocessing import LabelEncoder
import xgboost as xgb
import lightgbm as lgb
import warnings
warnings.filterwarnings('ignore')

# Veri yükleme
print("Veri yükleniyor...")
train_df = pd.read_csv('train.csv')
test_df = pd.read_csv('test.csv')
sample_submission = pd.read_csv('sample_submission.csv')

print(f"Train veri boyutu: {train_df.shape}")
print(f"Test veri boyutu: {test_df.shape}")
print(f"Sample submission boyutu: {sample_submission.shape}")

# Veri keşfi
print("\n=== VERİ KEŞFİ ===")
print("Train veri sütunları:")
print(train_df.columns.tolist())
print("\nTrain veri ilk 5 satır:")
print(train_df.head())

print("\nEvent type dağılımı:")
print(train_df['event_type'].value_counts())

print("\nSession value istatistikleri:")
print(train_df['session_value'].describe())

# Eksik değer kontrolü
print("\nEksik değerler:")
print("Train:", train_df.isnull().sum().sum())
print("Test:", test_df.isnull().sum().sum())

# Zaman sütununu datetime'a çevir
train_df['event_time'] = pd.to_datetime(train_df['event_time'])
test_df['event_time'] = pd.to_datetime(test_df['event_time'])

# Zaman özelliklerini çıkar
def extract_time_features(df):
    df['hour'] = df['event_time'].dt.hour
    df['day_of_week'] = df['event_time'].dt.dayofweek
    df['day_of_month'] = df['event_time'].dt.day
    df['month'] = df['event_time'].dt.month
    return df

train_df = extract_time_features(train_df)
test_df = extract_time_features(test_df)

# Session bazında özellik mühendisliği
def create_session_features(df):
    session_features = df.groupby('user_session').agg({
        'event_type': ['count', 'nunique'],
        'product_id': 'nunique',
        'category_id': 'nunique',
        'user_id': 'first',
        'hour': ['min', 'max', 'mean'],
        'day_of_week': 'first',
        'day_of_month': 'first',
        'month': 'first'
    }).reset_index()
    
    # Sütun isimlerini düzenle
    session_features.columns = ['user_session', 'total_events', 'unique_event_types',
                               'unique_products', 'unique_categories', 'user_id',
                               'min_hour', 'max_hour', 'mean_hour', 'day_of_week',
                               'day_of_month', 'month']
    
    # Event type bazında sayılar
    event_counts = df.groupby(['user_session', 'event_type']).size().unstack(fill_value=0)
    event_counts.columns = [f'count_{col}' for col in event_counts.columns]
    event_counts = event_counts.reset_index()
    
    # Birleştir
    session_features = session_features.merge(event_counts, on='user_session', how='left')
    session_features = session_features.fillna(0)
    
    return session_features

print("\nSession özelliklerini oluşturuyor...")
train_session_features = create_session_features(train_df)
test_session_features = create_session_features(test_df)

# Target değerini ekle
train_targets = train_df.groupby('user_session')['session_value'].first().reset_index()
train_session_features = train_session_features.merge(train_targets, on='user_session', how='left')

print(f"Train session features boyutu: {train_session_features.shape}")
print(f"Test session features boyutu: {test_session_features.shape}")

# Kategorik değişkenleri encode et
categorical_cols = ['user_id']
label_encoders = {}

for col in categorical_cols:
    if col in train_session_features.columns:
        le = LabelEncoder()
        # Train ve test'i birleştirerek fit et
        all_values = pd.concat([train_session_features[col], test_session_features[col]]).astype(str)
        le.fit(all_values)
        
        train_session_features[col] = le.transform(train_session_features[col].astype(str))
        test_session_features[col] = le.transform(test_session_features[col].astype(str))
        
        label_encoders[col] = le

# Özellik ve hedef değişkenleri ayır
feature_cols = [col for col in train_session_features.columns if col not in ['user_session', 'session_value']]
X = train_session_features[feature_cols]
y = train_session_features['session_value']
X_test = test_session_features[feature_cols]

print(f"\nÖzellik sayısı: {len(feature_cols)}")
print("Özellikler:", feature_cols)

# Train-validation split
X_train, X_val, y_train, y_val = train_test_split(X, y, test_size=0.2, random_state=42)

print(f"\nTrain boyutu: {X_train.shape}")
print(f"Validation boyutu: {X_val.shape}")
print(f"Test boyutu: {X_test.shape}")

# Model eğitimi
print("\n=== MODEL EĞİTİMİ ===")

# 1. Random Forest
print("Random Forest eğitiliyor...")
rf_model = RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1)
rf_model.fit(X_train, y_train)

rf_pred_val = rf_model.predict(X_val)
rf_rmse = np.sqrt(mean_squared_error(y_val, rf_pred_val))
rf_mae = mean_absolute_error(y_val, rf_pred_val)
rf_r2 = r2_score(y_val, rf_pred_val)

print(f"Random Forest - RMSE: {rf_rmse:.4f}, MAE: {rf_mae:.4f}, R2: {rf_r2:.4f}")

# 2. XGBoost
print("XGBoost eğitiliyor...")
xgb_model = xgb.XGBRegressor(n_estimators=100, random_state=42, n_jobs=-1)
xgb_model.fit(X_train, y_train)

xgb_pred_val = xgb_model.predict(X_val)
xgb_rmse = np.sqrt(mean_squared_error(y_val, xgb_pred_val))
xgb_mae = mean_absolute_error(y_val, xgb_pred_val)
xgb_r2 = r2_score(y_val, xgb_pred_val)

print(f"XGBoost - RMSE: {xgb_rmse:.4f}, MAE: {xgb_mae:.4f}, R2: {xgb_r2:.4f}")

# 3. LightGBM
print("LightGBM eğitiliyor...")
lgb_model = lgb.LGBMRegressor(n_estimators=100, random_state=42, n_jobs=-1, verbose=-1)
lgb_model.fit(X_train, y_train)

lgb_pred_val = lgb_model.predict(X_val)
lgb_rmse = np.sqrt(mean_squared_error(y_val, lgb_pred_val))
lgb_mae = mean_absolute_error(y_val, lgb_pred_val)
lgb_r2 = r2_score(y_val, lgb_pred_val)

print(f"LightGBM - RMSE: {lgb_rmse:.4f}, MAE: {lgb_mae:.4f}, R2: {lgb_r2:.4f}")

# En iyi modeli seç
models = {
    'RandomForest': (rf_model, rf_rmse),
    'XGBoost': (xgb_model, xgb_rmse),
    'LightGBM': (lgb_model, lgb_rmse)
}

best_model_name = min(models.keys(), key=lambda x: models[x][1])
best_model = models[best_model_name][0]
print(f"\nEn iyi model: {best_model_name}")

# Test tahminleri
print("\nTest tahminleri yapılıyor...")
test_predictions = best_model.predict(X_test)

# Submission dosyası oluştur
submission = pd.DataFrame({
    'user_session': test_session_features['user_session'],
    'session_value': test_predictions
})

submission.to_csv('submission.csv', index=False)
print("Submission dosyası 'submission.csv' olarak kaydedildi.")

print(f"\nTahmin istatistikleri:")
print(f"Min: {test_predictions.min():.4f}")
print(f"Max: {test_predictions.max():.4f}")
print(f"Mean: {test_predictions.mean():.4f}")
print(f"Std: {test_predictions.std():.4f}")
